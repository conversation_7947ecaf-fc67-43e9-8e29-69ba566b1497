#!/usr/bin/env python3
"""
Quick demo script to test the Road Element Annotation Assistant.
This script demonstrates the core functionality without requiring full setup.
"""

import os
import sys
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required packages can be imported."""
    logger.info("Testing package imports...")
    
    try:
        import numpy as np
        logger.info("✅ NumPy imported successfully")
        
        import pandas as pd
        logger.info("✅ Pandas imported successfully")
        
        import cv2
        logger.info("✅ OpenCV imported successfully")
        
        from PIL import Image
        logger.info("✅ Pillow imported successfully")
        
        import matplotlib.pyplot as plt
        logger.info("✅ Matplotlib imported successfully")
        
        import streamlit as st
        logger.info("✅ Streamlit imported successfully")
        
        # Test PyTorch import
        try:
            import torch
            logger.info(f"✅ PyTorch imported successfully (version: {torch.__version__})")
        except ImportError:
            logger.warning("⚠️ PyTorch not available - some features may not work")
        
        # Test YOLO import
        try:
            from ultralytics import YOLO
            logger.info("✅ Ultralytics YOLO imported successfully")
        except ImportError:
            logger.warning("⚠️ Ultralytics not available - auto-detection may not work")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False

def create_sample_data():
    """Create sample synthetic data for testing."""
    logger.info("Creating sample synthetic data...")
    
    try:
        import numpy as np
        from PIL import Image
        
        # Create data directories
        os.makedirs("data/raw", exist_ok=True)
        os.makedirs("data/processed", exist_ok=True)
        os.makedirs("data/annotations", exist_ok=True)
        
        # Create a simple synthetic road scene
        width, height = 640, 480
        
        # Create base image (sky blue background)
        img_array = np.full((height, width, 3), [135, 206, 235], dtype=np.uint8)
        
        # Add road (gray rectangle)
        road_y_start = int(height * 0.6)
        img_array[road_y_start:, :] = [64, 64, 64]  # Dark gray road
        
        # Add lane markings (white lines)
        lane_center = width // 2
        for y in range(road_y_start, height, 20):
            if y + 10 < height:
                img_array[y:y+10, lane_center-2:lane_center+2] = [255, 255, 255]
        
        # Add some buildings (rectangles)
        building_colors = [(139, 69, 19), (160, 82, 45), (105, 105, 105)]
        for i in range(3):
            x_start = i * (width // 3) + 20
            x_end = x_start + (width // 3) - 40
            y_start = int(height * 0.3)
            y_end = road_y_start
            
            color = building_colors[i % len(building_colors)]
            img_array[y_start:y_end, x_start:x_end] = color
        
        # Add some "vehicles" (colored rectangles)
        vehicle_positions = [
            (100, road_y_start + 20, 150, road_y_start + 60),  # Car 1
            (300, road_y_start + 30, 380, road_y_start + 80),  # Car 2
            (500, road_y_start + 15, 580, road_y_start + 70),  # Car 3
        ]
        
        vehicle_colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]  # Red, Green, Blue
        
        for i, (x1, y1, x2, y2) in enumerate(vehicle_positions):
            color = vehicle_colors[i % len(vehicle_colors)]
            img_array[y1:y2, x1:x2] = color
        
        # Save the synthetic image
        image = Image.fromarray(img_array)
        image_path = "data/raw/synthetic_road_01.jpg"
        image.save(image_path)
        
        logger.info(f"✅ Created synthetic road image: {image_path}")
        
        # Create a simple annotation file (YOLO format)
        annotation_path = "data/annotations/synthetic_road_01.txt"
        with open(annotation_path, 'w') as f:
            # Vehicle annotations (class 0 = vehicle)
            for i, (x1, y1, x2, y2) in enumerate(vehicle_positions):
                # Convert to YOLO format (normalized center coordinates + width/height)
                x_center = (x1 + x2) / 2 / width
                y_center = (y1 + y2) / 2 / height
                w = (x2 - x1) / width
                h = (y2 - y1) / height
                f.write(f"0 {x_center:.6f} {y_center:.6f} {w:.6f} {h:.6f}\n")
        
        logger.info(f"✅ Created annotation file: {annotation_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating sample data: {e}")
        return False

def test_annotation_utils():
    """Test the annotation utility functions."""
    logger.info("Testing annotation utilities...")
    
    try:
        # Add src to path
        sys.path.append(str(Path(__file__).parent / "src"))
        
        from src.utils.annotation_utils import calculate_iou, validate_bbox
        
        # Test IoU calculation
        bbox1 = [100, 100, 200, 200]
        bbox2 = [150, 150, 250, 250]
        iou = calculate_iou(bbox1, bbox2)
        logger.info(f"✅ IoU calculation test: {iou:.3f}")
        
        # Test bbox validation
        valid = validate_bbox(bbox1, 640, 480)
        logger.info(f"✅ Bbox validation test: {valid}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing annotation utils: {e}")
        return False

def test_image_processing():
    """Test basic image processing functionality."""
    logger.info("Testing image processing...")
    
    try:
        import cv2
        import numpy as np
        
        # Load the synthetic image we created
        image_path = "data/raw/synthetic_road_01.jpg"
        if os.path.exists(image_path):
            image = cv2.imread(image_path)
            if image is not None:
                logger.info(f"✅ Image loaded successfully: {image.shape}")
                
                # Test basic processing
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                logger.info(f"✅ Grayscale conversion: {gray.shape}")
                
                # Test resizing
                resized = cv2.resize(image, (320, 240))
                logger.info(f"✅ Image resizing: {resized.shape}")
                
                return True
            else:
                logger.error("❌ Failed to load image")
                return False
        else:
            logger.warning("⚠️ No sample image found - run create_sample_data first")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing image processing: {e}")
        return False

def show_project_structure():
    """Display the current project structure."""
    logger.info("Current project structure:")
    
    def print_tree(directory, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
        
        try:
            items = sorted(os.listdir(directory))
            for i, item in enumerate(items):
                if item.startswith('.'):
                    continue
                    
                path = os.path.join(directory, item)
                is_last = i == len(items) - 1
                current_prefix = "└── " if is_last else "├── "
                print(f"{prefix}{current_prefix}{item}")
                
                if os.path.isdir(path) and current_depth < max_depth - 1:
                    extension = "    " if is_last else "│   "
                    print_tree(path, prefix + extension, max_depth, current_depth + 1)
        except PermissionError:
            pass
    
    print_tree(".")

def main():
    """Main demo function."""
    print("🚗 Road Element Annotation Assistant - Quick Demo")
    print("=" * 60)
    
    # Test 1: Package imports
    if not test_imports():
        print("\n❌ Some required packages are missing. Please run:")
        print("   pip install -r requirements.txt")
        return 1
    
    # Test 2: Create sample data
    if not create_sample_data():
        print("\n❌ Failed to create sample data")
        return 1
    
    # Test 3: Test annotation utilities
    if not test_annotation_utils():
        print("\n⚠️ Annotation utilities test failed - some features may not work")
    
    # Test 4: Test image processing
    if not test_image_processing():
        print("\n⚠️ Image processing test failed")
    
    # Show project structure
    print("\n📁 Project Structure:")
    show_project_structure()
    
    print("\n🎉 Quick demo completed!")
    print("\n📋 Next steps:")
    print("1. Run the full setup: python setup.py")
    print("2. Launch the annotation interface: streamlit run src/annotation/app.py")
    print("3. Explore the analysis notebook: jupyter notebook notebooks/annotation_analysis.ipynb")
    print("4. Run the complete demo: python demo.py")
    
    print("\n💡 Key Features Demonstrated:")
    print("✅ Synthetic data generation")
    print("✅ Basic image processing")
    print("✅ Annotation format handling")
    print("✅ Project structure setup")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
