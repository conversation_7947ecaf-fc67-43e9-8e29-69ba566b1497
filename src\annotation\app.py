"""
Streamlit-based annotation interface for road elements.
"""

import streamlit as st
import cv2
import numpy as np
import os
import json
from PIL import Image
import pandas as pd
from typing import List, Dict, Optional

# Add parent directory to path for imports
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.models.yolo_detector import RoadElementDetector
from src.utils.image_utils import ImageProcessor, draw_bounding_box
from src.utils.annotation_utils import Annotation<PERSON>andler
from src.quality_assurance.validator import AnnotationValidator

# Page configuration
st.set_page_config(
    page_title="Road Element Annotation Assistant",
    page_icon="🚗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'current_image_index' not in st.session_state:
    st.session_state.current_image_index = 0
if 'annotations' not in st.session_state:
    st.session_state.annotations = {}
if 'detector' not in st.session_state:
    st.session_state.detector = None
if 'image_processor' not in st.session_state:
    st.session_state.image_processor = ImageProcessor()

def load_detector():
    """Load YOLO detector."""
    if st.session_state.detector is None:
        with st.spinner("Loading YOLO model..."):
            try:
                st.session_state.detector = RoadElementDetector()
                st.success("YOLO model loaded successfully!")
            except Exception as e:
                st.error(f"Error loading YOLO model: {str(e)}")
                return False
    return True

def get_image_files(directory: str) -> List[str]:
    """Get list of image files in directory."""
    if not os.path.exists(directory):
        return []
    
    extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    files = []
    
    for file in os.listdir(directory):
        if any(file.lower().endswith(ext) for ext in extensions):
            files.append(os.path.join(directory, file))
    
    return sorted(files)

def display_image_with_annotations(image: np.ndarray, annotations: List[Dict]) -> np.ndarray:
    """Display image with annotations overlaid."""
    vis_image = image.copy()
    
    colors = {
        'vehicle': (0, 255, 0),
        'pedestrian': (255, 0, 0),
        'signboard': (0, 0, 255),
        'traffic_light': (255, 255, 0),
        'lane_marking': (255, 0, 255)
    }
    
    for i, ann in enumerate(annotations):
        bbox = ann['bbox']
        class_name = ann['class_name']
        confidence = ann.get('confidence', 1.0)
        
        color = colors.get(class_name, (128, 128, 128))
        label = f"{class_name}: {confidence:.2f}"
        
        vis_image = draw_bounding_box(vis_image, bbox, label, color)
    
    return vis_image

def main():
    st.title("🚗 Road Element Annotation Assistant")
    st.markdown("Semi-automated annotation tool for autonomous driving datasets")
    
    # Sidebar configuration
    st.sidebar.header("Configuration")
    
    # Data directories
    data_dir = st.sidebar.text_input("Data Directory", value="data/raw")
    output_dir = st.sidebar.text_input("Output Directory", value="data/annotations")
    
    # Model settings
    confidence_threshold = st.sidebar.slider("Confidence Threshold", 0.1, 1.0, 0.5, 0.1)
    
    # Load images
    if os.path.exists(data_dir):
        image_files = get_image_files(data_dir)
        
        if not image_files:
            st.warning(f"No images found in {data_dir}")
            return
        
        st.sidebar.write(f"Found {len(image_files)} images")
        
        # Image navigation
        col1, col2, col3 = st.sidebar.columns(3)
        
        with col1:
            if st.button("⬅️ Previous"):
                if st.session_state.current_image_index > 0:
                    st.session_state.current_image_index -= 1
        
        with col2:
            st.write(f"{st.session_state.current_image_index + 1}/{len(image_files)}")
        
        with col3:
            if st.button("➡️ Next"):
                if st.session_state.current_image_index < len(image_files) - 1:
                    st.session_state.current_image_index += 1
        
        # Current image
        current_image_path = image_files[st.session_state.current_image_index]
        image_name = os.path.basename(current_image_path)
        
        st.subheader(f"Current Image: {image_name}")
        
        # Load and display image
        image = st.session_state.image_processor.load_image(current_image_path)
        
        if image is None:
            st.error("Failed to load image")
            return
        
        # Get current annotations
        current_annotations = st.session_state.annotations.get(image_name, [])
        
        # Main interface
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Auto-detection
            if st.button("🤖 Auto-Detect Objects"):
                if load_detector():
                    with st.spinner("Detecting objects..."):
                        detections = st.session_state.detector.detect(image)
                        # Filter by confidence
                        detections = [d for d in detections if d['confidence'] >= confidence_threshold]
                        st.session_state.annotations[image_name] = detections
                        current_annotations = detections
                        st.success(f"Detected {len(detections)} objects")
            
            # Display image
            if current_annotations:
                vis_image = display_image_with_annotations(image, current_annotations)
                st.image(vis_image, caption="Image with Annotations", use_column_width=True)
            else:
                st.image(image, caption="Original Image", use_column_width=True)
        
        with col2:
            st.subheader("Annotations")
            
            # Display current annotations
            if current_annotations:
                for i, ann in enumerate(current_annotations):
                    with st.expander(f"{ann['class_name']} #{i+1}"):
                        st.write(f"**Class:** {ann['class_name']}")
                        st.write(f"**Confidence:** {ann['confidence']:.2f}")
                        st.write(f"**Bbox:** {[round(x, 1) for x in ann['bbox']]}")
                        
                        # Edit options
                        new_class = st.selectbox(
                            "Class", 
                            ['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'],
                            index=['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'].index(ann['class_name']),
                            key=f"class_{i}"
                        )
                        
                        if new_class != ann['class_name']:
                            st.session_state.annotations[image_name][i]['class_name'] = new_class
                            st.rerun()
                        
                        if st.button(f"Delete #{i+1}", key=f"delete_{i}"):
                            st.session_state.annotations[image_name].pop(i)
                            st.rerun()
            else:
                st.info("No annotations yet. Use auto-detect or add manually.")
            
            # Manual annotation (simplified)
            st.subheader("Add Manual Annotation")
            with st.form("manual_annotation"):
                new_class = st.selectbox("Class", ['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'])
                
                col_x1, col_y1 = st.columns(2)
                with col_x1:
                    x1 = st.number_input("X1", min_value=0, max_value=image.shape[1], value=0)
                with col_y1:
                    y1 = st.number_input("Y1", min_value=0, max_value=image.shape[0], value=0)
                
                col_x2, col_y2 = st.columns(2)
                with col_x2:
                    x2 = st.number_input("X2", min_value=0, max_value=image.shape[1], value=100)
                with col_y2:
                    y2 = st.number_input("Y2", min_value=0, max_value=image.shape[0], value=100)
                
                if st.form_submit_button("Add Annotation"):
                    if image_name not in st.session_state.annotations:
                        st.session_state.annotations[image_name] = []
                    
                    new_annotation = {
                        'bbox': [x1, y1, x2, y2],
                        'class_name': new_class,
                        'confidence': 1.0,
                        'class_id': ['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'].index(new_class)
                    }
                    
                    st.session_state.annotations[image_name].append(new_annotation)
                    st.success("Annotation added!")
                    st.rerun()
        
        # Save annotations
        st.subheader("Save & Export")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("💾 Save Current"):
                if current_annotations:
                    # Save in YOLO format
                    os.makedirs(output_dir, exist_ok=True)
                    
                    annotation_handler = AnnotationHandler()
                    output_path = os.path.join(output_dir, os.path.splitext(image_name)[0] + '.txt')
                    
                    success = annotation_handler.save_yolo_annotation(
                        current_annotations, output_path, image.shape[1], image.shape[0]
                    )
                    
                    if success:
                        st.success("Annotations saved!")
                    else:
                        st.error("Failed to save annotations")
                else:
                    st.warning("No annotations to save")
        
        with col2:
            if st.button("📊 Validate Quality"):
                if current_annotations:
                    validator = AnnotationValidator()
                    results = validator.validate_single_annotation(
                        current_annotations, image.shape[1], image.shape[0], image_name
                    )
                    
                    st.metric("Quality Score", f"{results['quality_score']:.2f}")
                    
                    if results['errors']:
                        st.error("Errors found:")
                        for error in results['errors']:
                            st.write(f"- {error}")
                    
                    if results['warnings']:
                        st.warning("Warnings:")
                        for warning in results['warnings']:
                            st.write(f"- {warning}")
                else:
                    st.info("No annotations to validate")
        
        with col3:
            if st.button("📈 Dataset Stats"):
                # Show dataset statistics
                total_annotations = sum(len(anns) for anns in st.session_state.annotations.values())
                annotated_images = len(st.session_state.annotations)
                
                st.metric("Annotated Images", annotated_images)
                st.metric("Total Annotations", total_annotations)
                
                # Class distribution
                class_counts = {}
                for anns in st.session_state.annotations.values():
                    for ann in anns:
                        class_name = ann['class_name']
                        class_counts[class_name] = class_counts.get(class_name, 0) + 1
                
                if class_counts:
                    st.bar_chart(pd.Series(class_counts))
    
    else:
        st.error(f"Data directory not found: {data_dir}")
        st.info("Please create the data directory and add some images to get started.")

if __name__ == "__main__":
    main()
