"""
Simplified Streamlit annotation interface (without PyTorch dependencies).
"""

import streamlit as st
import cv2
import numpy as np
import os
import json
from PIL import Image
import pandas as pd
from typing import List, Dict, Optional

# Page configuration
st.set_page_config(
    page_title="Road Element Annotation Assistant (Demo)",
    page_icon="🚗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'current_image_index' not in st.session_state:
    st.session_state.current_image_index = 0
if 'annotations' not in st.session_state:
    st.session_state.annotations = {}

def get_image_files(directory: str) -> List[str]:
    """Get list of image files in directory."""
    if not os.path.exists(directory):
        return []
    
    extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    files = []
    
    for file in os.listdir(directory):
        if any(file.lower().endswith(ext) for ext in extensions):
            files.append(os.path.join(directory, file))
    
    return sorted(files)

def load_image(image_path: str) -> Optional[np.ndarray]:
    """Load an image from file path."""
    try:
        image = cv2.imread(image_path)
        if image is None:
            return None
        # Convert BGR to RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image
    except Exception as e:
        st.error(f"Error loading image: {e}")
        return None

def draw_bounding_box(image: np.ndarray, bbox: List[float], 
                     label: str = "", color: tuple = (0, 255, 0)) -> np.ndarray:
    """Draw bounding box on image."""
    image_copy = image.copy()
    x1, y1, x2, y2 = map(int, bbox)
    
    # Draw rectangle
    cv2.rectangle(image_copy, (x1, y1), (x2, y2), color, 2)
    
    # Draw label
    if label:
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
        cv2.rectangle(image_copy, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), color, -1)
        cv2.putText(image_copy, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return image_copy

def display_image_with_annotations(image: np.ndarray, annotations: List[Dict]) -> np.ndarray:
    """Display image with annotations overlaid."""
    vis_image = image.copy()
    
    colors = {
        'vehicle': (0, 255, 0),
        'pedestrian': (255, 0, 0),
        'signboard': (0, 0, 255),
        'traffic_light': (255, 255, 0),
        'lane_marking': (255, 0, 255)
    }
    
    for i, ann in enumerate(annotations):
        bbox = ann['bbox']
        class_name = ann['class_name']
        confidence = ann.get('confidence', 1.0)
        
        color = colors.get(class_name, (128, 128, 128))
        label = f"{class_name}: {confidence:.2f}"
        
        vis_image = draw_bounding_box(vis_image, bbox, label, color)
    
    return vis_image

def create_demo_annotations(image_shape: tuple) -> List[Dict]:
    """Create demo annotations for the synthetic image."""
    height, width = image_shape[:2]
    
    # These are the vehicle positions from our synthetic image
    vehicle_positions = [
        (100, int(height * 0.6) + 20, 150, int(height * 0.6) + 60),  # Car 1
        (300, int(height * 0.6) + 30, 380, int(height * 0.6) + 80),  # Car 2
        (500, int(height * 0.6) + 15, 580, int(height * 0.6) + 70),  # Car 3
    ]
    
    annotations = []
    for i, (x1, y1, x2, y2) in enumerate(vehicle_positions):
        annotations.append({
            'bbox': [x1, y1, x2, y2],
            'class_name': 'vehicle',
            'confidence': 0.9 - i * 0.1,  # Decreasing confidence
            'class_id': 0
        })
    
    return annotations

def save_yolo_annotation(annotations: List[Dict], output_path: str,
                        image_width: int, image_height: int) -> bool:
    """Save annotations in YOLO format."""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w') as f:
            for ann in annotations:
                x1, y1, x2, y2 = ann['bbox']
                
                # Convert to YOLO format
                x_center = (x1 + x2) / 2 / image_width
                y_center = (y1 + y2) / 2 / image_height
                width = (x2 - x1) / image_width
                height = (y2 - y1) / image_height
                
                class_id = ann.get('class_id', 0)
                
                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} "
                       f"{width:.6f} {height:.6f}\n")
        
        return True
    
    except Exception as e:
        st.error(f"Error saving annotation: {e}")
        return False

def main():
    st.title("🚗 Road Element Annotation Assistant (Demo)")
    st.markdown("**Simplified demo version** - Semi-automated annotation tool for autonomous driving datasets")
    
    # Sidebar configuration
    st.sidebar.header("Configuration")
    
    # Data directories
    data_dir = st.sidebar.text_input("Data Directory", value="data/raw")
    output_dir = st.sidebar.text_input("Output Directory", value="data/annotations")
    
    # Load images
    if os.path.exists(data_dir):
        image_files = get_image_files(data_dir)
        
        if not image_files:
            st.warning(f"No images found in {data_dir}")
            st.info("Run `python quick_demo.py` to create sample data")
            return
        
        st.sidebar.write(f"Found {len(image_files)} images")
        
        # Image navigation
        col1, col2, col3 = st.sidebar.columns(3)
        
        with col1:
            if st.button("⬅️ Previous"):
                if st.session_state.current_image_index > 0:
                    st.session_state.current_image_index -= 1
        
        with col2:
            st.write(f"{st.session_state.current_image_index + 1}/{len(image_files)}")
        
        with col3:
            if st.button("➡️ Next"):
                if st.session_state.current_image_index < len(image_files) - 1:
                    st.session_state.current_image_index += 1
        
        # Current image
        current_image_path = image_files[st.session_state.current_image_index]
        image_name = os.path.basename(current_image_path)
        
        st.subheader(f"Current Image: {image_name}")
        
        # Load and display image
        image = load_image(current_image_path)
        
        if image is None:
            st.error("Failed to load image")
            return
        
        # Get current annotations
        current_annotations = st.session_state.annotations.get(image_name, [])
        
        # Main interface
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Demo auto-detection
            if st.button("🤖 Demo Auto-Detect (Synthetic Data)"):
                if "synthetic" in image_name.lower():
                    demo_annotations = create_demo_annotations(image.shape)
                    st.session_state.annotations[image_name] = demo_annotations
                    current_annotations = demo_annotations
                    st.success(f"Demo: Detected {len(demo_annotations)} vehicles")
                else:
                    st.info("Demo auto-detection only works with synthetic images. "
                           "Install PyTorch and ultralytics for real YOLO detection.")
            
            # Display image
            if current_annotations:
                vis_image = display_image_with_annotations(image, current_annotations)
                st.image(vis_image, caption="Image with Annotations", use_column_width=True)
            else:
                st.image(image, caption="Original Image", use_column_width=True)
        
        with col2:
            st.subheader("Annotations")
            
            # Display current annotations
            if current_annotations:
                for i, ann in enumerate(current_annotations):
                    with st.expander(f"{ann['class_name']} #{i+1}"):
                        st.write(f"**Class:** {ann['class_name']}")
                        st.write(f"**Confidence:** {ann['confidence']:.2f}")
                        st.write(f"**Bbox:** {[round(x, 1) for x in ann['bbox']]}")
                        
                        # Edit options
                        new_class = st.selectbox(
                            "Class", 
                            ['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'],
                            index=['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'].index(ann['class_name']),
                            key=f"class_{i}"
                        )
                        
                        if new_class != ann['class_name']:
                            st.session_state.annotations[image_name][i]['class_name'] = new_class
                            st.rerun()
                        
                        if st.button(f"Delete #{i+1}", key=f"delete_{i}"):
                            st.session_state.annotations[image_name].pop(i)
                            st.rerun()
            else:
                st.info("No annotations yet. Use demo auto-detect or add manually.")
            
            # Manual annotation
            st.subheader("Add Manual Annotation")
            with st.form("manual_annotation"):
                new_class = st.selectbox("Class", ['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'])
                
                col_x1, col_y1 = st.columns(2)
                with col_x1:
                    x1 = st.number_input("X1", min_value=0, max_value=image.shape[1], value=0)
                with col_y1:
                    y1 = st.number_input("Y1", min_value=0, max_value=image.shape[0], value=0)
                
                col_x2, col_y2 = st.columns(2)
                with col_x2:
                    x2 = st.number_input("X2", min_value=0, max_value=image.shape[1], value=100)
                with col_y2:
                    y2 = st.number_input("Y2", min_value=0, max_value=image.shape[0], value=100)
                
                if st.form_submit_button("Add Annotation"):
                    if image_name not in st.session_state.annotations:
                        st.session_state.annotations[image_name] = []
                    
                    new_annotation = {
                        'bbox': [x1, y1, x2, y2],
                        'class_name': new_class,
                        'confidence': 1.0,
                        'class_id': ['vehicle', 'pedestrian', 'signboard', 'traffic_light', 'lane_marking'].index(new_class)
                    }
                    
                    st.session_state.annotations[image_name].append(new_annotation)
                    st.success("Annotation added!")
                    st.rerun()
        
        # Save annotations
        st.subheader("Save & Export")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("💾 Save Current"):
                if current_annotations:
                    # Save in YOLO format
                    os.makedirs(output_dir, exist_ok=True)
                    
                    output_path = os.path.join(output_dir, os.path.splitext(image_name)[0] + '.txt')
                    
                    success = save_yolo_annotation(
                        current_annotations, output_path, image.shape[1], image.shape[0]
                    )
                    
                    if success:
                        st.success("Annotations saved!")
                    else:
                        st.error("Failed to save annotations")
                else:
                    st.warning("No annotations to save")
        
        with col2:
            if st.button("📊 Basic Stats"):
                if current_annotations:
                    st.metric("Annotations", len(current_annotations))
                    
                    # Class distribution
                    class_counts = {}
                    for ann in current_annotations:
                        class_name = ann['class_name']
                        class_counts[class_name] = class_counts.get(class_name, 0) + 1
                    
                    st.write("**Class Distribution:**")
                    for class_name, count in class_counts.items():
                        st.write(f"- {class_name}: {count}")
                else:
                    st.info("No annotations to analyze")
        
        with col3:
            if st.button("📈 Dataset Stats"):
                # Show dataset statistics
                total_annotations = sum(len(anns) for anns in st.session_state.annotations.values())
                annotated_images = len(st.session_state.annotations)
                
                st.metric("Annotated Images", annotated_images)
                st.metric("Total Annotations", total_annotations)
                
                # Class distribution
                class_counts = {}
                for anns in st.session_state.annotations.values():
                    for ann in anns:
                        class_name = ann['class_name']
                        class_counts[class_name] = class_counts.get(class_name, 0) + 1
                
                if class_counts:
                    st.bar_chart(pd.Series(class_counts))
    
    else:
        st.error(f"Data directory not found: {data_dir}")
        st.info("Please run `python quick_demo.py` to create sample data.")
        
        # Show instructions
        st.subheader("Getting Started")
        st.markdown("""
        1. **Create sample data**: Run `python quick_demo.py`
        2. **Install full dependencies**: Run `pip install torch ultralytics` for YOLO detection
        3. **Use the full app**: Run `streamlit run src/annotation/app.py`
        
        **This demo version includes:**
        - ✅ Manual annotation interface
        - ✅ Demo auto-detection for synthetic images
        - ✅ YOLO format export
        - ✅ Basic statistics
        
        **Full version adds:**
        - 🤖 Real YOLO object detection
        - 📊 Advanced quality assurance
        - 📈 Comprehensive analytics
        """)

if __name__ == "__main__":
    main()
