"""
YOLO-based object detector for road elements.
"""

import torch
import numpy as np
from ultralytics import YOLO
from typing import List, Dict, Optional, Tuple
import logging
import cv2

logger = logging.getLogger(__name__)

class RoadElementDetector:
    """YOLO-based detector for road elements."""
    
    def __init__(self, model_path: str = 'yolov8n.pt', confidence_threshold: float = 0.5):
        """
        Initialize the road element detector.
        
        Args:
            model_path: Path to YOLO model weights
            confidence_threshold: Minimum confidence for detections
        """
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Road element class mapping (COCO classes relevant to road scenes)
        self.road_class_mapping = {
            0: 'person',        # pedestrian
            1: 'bicycle',       # vehicle
            2: 'car',          # vehicle
            3: 'motorcycle',   # vehicle
            5: 'bus',          # vehicle
            7: 'truck',        # vehicle
            9: 'traffic light', # traffic_light
            11: 'stop sign',   # signboard
            12: 'parking meter', # signboard
        }
        
        # Map to our custom classes
        self.custom_class_mapping = {
            'person': 'pedestrian',
            'bicycle': 'vehicle',
            'car': 'vehicle',
            'motorcycle': 'vehicle',
            'bus': 'vehicle',
            'truck': 'vehicle',
            'traffic light': 'traffic_light',
            'stop sign': 'signboard',
            'parking meter': 'signboard'
        }
        
        self.load_model(model_path)
    
    def load_model(self, model_path: str) -> bool:
        """
        Load YOLO model.
        
        Args:
            model_path: Path to model weights
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.model = YOLO(model_path)
            logger.info(f"YOLO model loaded successfully: {model_path}")
            logger.info(f"Using device: {self.device}")
            return True
        
        except Exception as e:
            logger.error(f"Error loading YOLO model: {str(e)}")
            return False
    
    def detect(self, image: np.ndarray) -> List[Dict]:
        """
        Detect road elements in image.
        
        Args:
            image: Input image (RGB format)
            
        Returns:
            List of detection dictionaries
        """
        if self.model is None:
            logger.error("Model not loaded")
            return []
        
        try:
            # Run inference
            results = self.model(image, conf=self.confidence_threshold, verbose=False)
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract box information
                        xyxy = box.xyxy[0].cpu().numpy()  # x1, y1, x2, y2
                        conf = box.conf[0].cpu().numpy()
                        cls = int(box.cls[0].cpu().numpy())
                        
                        # Get class name
                        class_name = self.model.names[cls]
                        
                        # Filter for road-relevant classes
                        if class_name in self.custom_class_mapping:
                            custom_class = self.custom_class_mapping[class_name]
                            
                            detection = {
                                'bbox': xyxy.tolist(),
                                'confidence': float(conf),
                                'class_name': custom_class,
                                'original_class': class_name,
                                'class_id': self._get_custom_class_id(custom_class)
                            }
                            detections.append(detection)
            
            logger.info(f"Detected {len(detections)} road elements")
            return detections
        
        except Exception as e:
            logger.error(f"Error during detection: {str(e)}")
            return []
    
    def _get_custom_class_id(self, class_name: str) -> int:
        """Get custom class ID for our road element classes."""
        class_ids = {
            'vehicle': 0,
            'pedestrian': 1,
            'signboard': 2,
            'traffic_light': 3,
            'lane_marking': 4
        }
        return class_ids.get(class_name, 0)
    
    def detect_batch(self, images: List[np.ndarray]) -> List[List[Dict]]:
        """
        Detect road elements in batch of images.
        
        Args:
            images: List of input images
            
        Returns:
            List of detection lists for each image
        """
        all_detections = []
        
        for image in images:
            detections = self.detect(image)
            all_detections.append(detections)
        
        return all_detections
    
    def visualize_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Visualize detections on image.
        
        Args:
            image: Input image
            detections: List of detections
            
        Returns:
            Image with detections drawn
        """
        vis_image = image.copy()
        
        # Color mapping for different classes
        colors = {
            'vehicle': (0, 255, 0),      # Green
            'pedestrian': (255, 0, 0),   # Red
            'signboard': (0, 0, 255),    # Blue
            'traffic_light': (255, 255, 0), # Yellow
            'lane_marking': (255, 0, 255)   # Magenta
        }
        
        for detection in detections:
            bbox = detection['bbox']
            class_name = detection['class_name']
            confidence = detection['confidence']
            
            x1, y1, x2, y2 = map(int, bbox)
            color = colors.get(class_name, (128, 128, 128))
            
            # Draw bounding box
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), color, 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            
            cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(vis_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return vis_image
    
    def filter_detections(self, detections: List[Dict], 
                         min_confidence: float = None,
                         allowed_classes: List[str] = None) -> List[Dict]:
        """
        Filter detections based on confidence and class.
        
        Args:
            detections: List of detections
            min_confidence: Minimum confidence threshold
            allowed_classes: List of allowed class names
            
        Returns:
            Filtered detections
        """
        filtered = detections.copy()
        
        if min_confidence is not None:
            filtered = [d for d in filtered if d['confidence'] >= min_confidence]
        
        if allowed_classes is not None:
            filtered = [d for d in filtered if d['class_name'] in allowed_classes]
        
        return filtered

def non_max_suppression(detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
    """
    Apply Non-Maximum Suppression to remove overlapping detections.
    
    Args:
        detections: List of detections
        iou_threshold: IoU threshold for suppression
        
    Returns:
        Filtered detections after NMS
    """
    if not detections:
        return []
    
    # Sort by confidence
    detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
    
    keep = []
    
    while detections:
        # Keep the detection with highest confidence
        current = detections.pop(0)
        keep.append(current)
        
        # Remove detections with high IoU
        remaining = []
        for detection in detections:
            iou = calculate_iou(current['bbox'], detection['bbox'])
            if iou < iou_threshold:
                remaining.append(detection)
        
        detections = remaining
    
    return keep

def calculate_iou(bbox1: List[float], bbox2: List[float]) -> float:
    """Calculate IoU between two bounding boxes."""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # Calculate intersection
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    intersection = (x2_i - x1_i) * (y2_i - y1_i)
    
    # Calculate union
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0
