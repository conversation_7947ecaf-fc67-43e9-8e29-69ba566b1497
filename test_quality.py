#!/usr/bin/env python3
"""
Test script for quality assurance functionality.
"""

import sys
import os
sys.path.append('src')

# Import with absolute paths
from src.utils.annotation_utils import AnnotationHandler, calculate_iou
from src.utils.image_utils import ImageProcessor

def test_quality_assurance():
    """Test the quality assurance functionality."""
    print("🔍 Testing Quality Assurance System")
    print("=" * 50)
    
    # Initialize components
    handler = AnnotationHandler()
    processor = ImageProcessor()
    
    # Load sample annotation
    ann_path = 'data/annotations/synthetic_road_01.txt'
    if not os.path.exists(ann_path):
        print("❌ Sample annotation file not found. Run quick_demo.py first.")
        return
    
    # Load annotations
    annotations = handler.load_yolo_annotation(ann_path, 640, 480)
    print(f"📝 Loaded {len(annotations)} annotations")
    
    # Display annotation details
    for i, ann in enumerate(annotations):
        print(f"  {i+1}. {ann['class_name']}: {ann['bbox']}")
    
    # Test IoU calculation
    if len(annotations) >= 2:
        bbox1 = annotations[0]['bbox']
        bbox2 = annotations[1]['bbox']
        iou = calculate_iou(bbox1, bbox2)
        print(f"\n📊 IoU between first two annotations: {iou:.3f}")
    
    # Basic quality checks
    print(f"\n✅ Quality Checks:")
    
    valid_count = 0
    for i, ann in enumerate(annotations):
        bbox = ann['bbox']
        x1, y1, x2, y2 = bbox
        
        # Check if bbox is valid
        if x1 < x2 and y1 < y2 and x1 >= 0 and y1 >= 0 and x2 <= 640 and y2 <= 480:
            valid_count += 1
            print(f"  ✅ Annotation {i+1}: Valid bounding box")
        else:
            print(f"  ❌ Annotation {i+1}: Invalid bounding box")
        
        # Check area
        area = (x2 - x1) * (y2 - y1)
        print(f"     Area: {area:.0f} pixels")
        
        # Check aspect ratio
        aspect_ratio = (x2 - x1) / (y2 - y1) if (y2 - y1) > 0 else 0
        print(f"     Aspect ratio: {aspect_ratio:.2f}")
    
    # Calculate quality score
    quality_score = valid_count / len(annotations) if annotations else 0
    print(f"\n📈 Overall Quality Score: {quality_score:.2f} ({valid_count}/{len(annotations)} valid)")
    
    # Class distribution
    class_counts = {}
    for ann in annotations:
        class_name = ann['class_name']
        class_counts[class_name] = class_counts.get(class_name, 0) + 1
    
    print(f"\n📊 Class Distribution:")
    for class_name, count in class_counts.items():
        percentage = (count / len(annotations)) * 100
        print(f"  {class_name}: {count} ({percentage:.1f}%)")
    
    print(f"\n🎉 Quality assurance test completed!")
    return True

if __name__ == "__main__":
    test_quality_assurance()
