#!/usr/bin/env python3
"""
Test real YOLO detection on traffic images.
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append('src')

def test_yolo_detection():
    """Test YOLO detection on real traffic images."""
    print("🚗 Testing YOLO Detection on Real Traffic Images")
    print("=" * 60)
    
    try:
        from src.models.yolo_detector import RoadElementDetector
        from src.utils.image_utils import ImageProcessor
        
        # Initialize components
        print("🔧 Initializing YOLO detector...")
        detector = RoadElementDetector()
        processor = ImageProcessor()
        
        # Get real traffic images
        image_dir = "data/raw"
        image_files = [f for f in os.listdir(image_dir) 
                      if f.endswith(('.jpg', '.jpeg', '.png')) and not f.startswith('synthetic')]
        
        print(f"📁 Found {len(image_files)} real traffic images")
        
        total_detections = 0
        results_summary = []
        
        for i, img_file in enumerate(image_files):
            print(f"\n📸 Processing {i+1}/{len(image_files)}: {img_file}")
            
            # Load image
            img_path = os.path.join(image_dir, img_file)
            image = processor.load_image(img_path)
            
            if image is None:
                print(f"   ❌ Failed to load image")
                continue
            
            print(f"   📏 Image size: {image.shape[1]}x{image.shape[0]}")
            
            # Run detection
            detections = detector.detect(image)
            print(f"   🎯 Detected {len(detections)} objects")
            
            # Show detection details
            class_counts = {}
            for detection in detections:
                class_name = detection['class_name']
                confidence = detection['confidence']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                print(f"      - {class_name}: {confidence:.2f}")
            
            # Summary for this image
            result = {
                'image': img_file,
                'detections': len(detections),
                'classes': class_counts
            }
            results_summary.append(result)
            total_detections += len(detections)
            
            # Save annotated image
            if detections:
                vis_image = detector.visualize_detections(image, detections)
                output_path = f"data/real_processed/annotated_{img_file}"
                os.makedirs("data/real_processed", exist_ok=True)
                
                # Convert RGB to BGR for saving
                vis_image_bgr = cv2.cvtColor(vis_image, cv2.COLOR_RGB2BGR)
                cv2.imwrite(output_path, vis_image_bgr)
                print(f"   💾 Saved annotated image: {output_path}")
        
        # Overall summary
        print(f"\n📊 Detection Summary")
        print("=" * 40)
        print(f"Total images processed: {len(image_files)}")
        print(f"Total detections: {total_detections}")
        print(f"Average detections per image: {total_detections/len(image_files):.1f}")
        
        # Class distribution across all images
        all_classes = {}
        for result in results_summary:
            for class_name, count in result['classes'].items():
                all_classes[class_name] = all_classes.get(class_name, 0) + count
        
        print(f"\n🏷️ Class Distribution:")
        for class_name, count in all_classes.items():
            percentage = (count / total_detections) * 100 if total_detections > 0 else 0
            print(f"   {class_name}: {count} ({percentage:.1f}%)")
        
        # Per-image breakdown
        print(f"\n📋 Per-Image Results:")
        for result in results_summary:
            print(f"   {result['image']}: {result['detections']} objects")
            for class_name, count in result['classes'].items():
                print(f"      - {class_name}: {count}")
        
        print(f"\n✅ Real traffic image detection test completed!")
        print(f"📁 Annotated images saved to: data/real_processed/")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure ultralytics is installed: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ Error during detection: {e}")
        return False

def compare_with_synthetic():
    """Compare detection results with synthetic image."""
    print(f"\n🔄 Comparing with Synthetic Image")
    print("=" * 40)
    
    try:
        from src.models.yolo_detector import RoadElementDetector
        from src.utils.image_utils import ImageProcessor
        
        detector = RoadElementDetector()
        processor = ImageProcessor()
        
        # Test synthetic image
        synthetic_path = "data/raw/synthetic_road_01.jpg"
        if os.path.exists(synthetic_path):
            image = processor.load_image(synthetic_path)
            if image is not None:
                detections = detector.detect(image)
                print(f"🎨 Synthetic image: {len(detections)} detections")
                
                for detection in detections:
                    print(f"   - {detection['class_name']}: {detection['confidence']:.2f}")
        
        print(f"\n💡 Observations:")
        print("   - Real images typically have more complex scenes")
        print("   - Detection confidence may vary based on image quality")
        print("   - Real images test the robustness of the annotation pipeline")
        
    except Exception as e:
        print(f"❌ Error comparing with synthetic: {e}")

def main():
    """Main function."""
    success = test_yolo_detection()
    
    if success:
        compare_with_synthetic()
        
        print(f"\n🚀 Next Steps:")
        print("1. 🌐 Open the annotation interface:")
        print("   http://localhost:8504")
        print("2. 🖼️ Navigate through the real traffic images")
        print("3. 🤖 Test the 'Demo Auto-Detect' button (works on synthetic)")
        print("4. ✏️ Add manual annotations to real images")
        print("5. 📊 Compare annotation quality between image types")
        
        print(f"\n🔧 For full YOLO detection in the interface:")
        print("   Use the full app: streamlit run src/annotation/app.py")
    else:
        print(f"\n❌ Detection test failed")

if __name__ == "__main__":
    main()
