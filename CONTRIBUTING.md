# Contributing to Road Element Annotation Assistant

Thank you for your interest in contributing to the Road Element Annotation Assistant! This document provides guidelines for contributing to the project.

## 🚀 Getting Started

### Prerequisites
- Python 3.8 or higher
- Git
- Basic knowledge of computer vision and machine learning

### Development Setup
1. **Fork the repository**
   ```bash
   git clone https://github.com/yourusername/road-element-annotation-assistant.git
   cd road-element-annotation-assistant
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -e .  # Install in development mode
   ```

4. **Run tests**
   ```bash
   python quick_demo.py
   python -m pytest tests/
   ```

## 📋 How to Contribute

### 1. Reporting Issues
- Use the GitHub issue tracker
- Provide detailed description of the problem
- Include steps to reproduce
- Add relevant screenshots or error messages

### 2. Feature Requests
- Open an issue with the "enhancement" label
- Describe the feature and its benefits
- Discuss implementation approach

### 3. Code Contributions

#### Branch Naming Convention
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

#### Pull Request Process
1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the coding standards
   - Add tests for new functionality
   - Update documentation

3. **Test your changes**
   ```bash
   python -m pytest tests/
   python test_quality.py
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new annotation feature"
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 🎯 Development Guidelines

### Code Style
- Follow PEP 8 style guide
- Use Black for code formatting: `black src/ tests/`
- Use type hints where appropriate
- Maximum line length: 88 characters

### Testing
- Write unit tests for new functions
- Maintain test coverage above 80%
- Test both success and failure cases
- Use descriptive test names

### Documentation
- Update README.md for new features
- Add docstrings to all functions and classes
- Include examples in docstrings
- Update API documentation

### Commit Messages
Follow conventional commit format:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `test:` - Test additions/modifications
- `refactor:` - Code refactoring
- `style:` - Code style changes

## 🏗️ Project Structure

```
src/
├── annotation/          # Annotation interface components
├── models/             # ML models and detection
├── quality_assurance/  # QA and validation
└── utils/              # Utility functions

tests/                  # Unit tests
notebooks/              # Analysis notebooks
docs/                   # Documentation
.github/                # GitHub workflows
```

## 🔍 Areas for Contribution

### High Priority
- **Performance optimization** - Speed up annotation processing
- **New annotation formats** - Support for additional formats
- **Advanced QA metrics** - More sophisticated quality measures
- **Mobile interface** - Responsive design improvements

### Medium Priority
- **Batch processing** - Handle multiple images efficiently
- **Export options** - Additional export formats
- **Visualization improvements** - Better charts and graphs
- **Documentation** - More examples and tutorials

### Low Priority
- **Integration tests** - End-to-end testing
- **Deployment scripts** - Docker, cloud deployment
- **Performance benchmarks** - Speed and accuracy metrics

## 🧪 Testing Guidelines

### Unit Tests
```python
def test_calculate_iou():
    """Test IoU calculation with known values."""
    bbox1 = [0, 0, 10, 10]
    bbox2 = [5, 5, 15, 15]
    expected_iou = 0.25  # 25/100
    assert abs(calculate_iou(bbox1, bbox2) - expected_iou) < 0.01
```

### Integration Tests
- Test complete annotation workflows
- Verify file I/O operations
- Check UI component interactions

### Performance Tests
- Measure annotation processing speed
- Monitor memory usage
- Test with large datasets

## 📚 Resources

### Learning Materials
- [Computer Vision Fundamentals](https://opencv.org/)
- [YOLO Object Detection](https://ultralytics.com/)
- [Streamlit Documentation](https://docs.streamlit.io/)
- [PyTorch Tutorials](https://pytorch.org/tutorials/)

### Related Projects
- [LabelImg](https://github.com/tzutalin/labelImg)
- [CVAT](https://github.com/openvinotoolkit/cvat)
- [Roboflow](https://roboflow.com/)

## 🤝 Community

### Communication
- GitHub Discussions for general questions
- Issues for bug reports and feature requests
- Pull requests for code contributions

### Code of Conduct
- Be respectful and inclusive
- Provide constructive feedback
- Help newcomers get started
- Focus on the project's goals

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

## 📞 Getting Help

If you need help:
1. Check existing documentation
2. Search GitHub issues
3. Create a new issue with detailed description
4. Join community discussions

Thank you for contributing to making road element annotation more efficient and accurate! 🚗✨
