#!/usr/bin/env python3
"""
Download real traffic images for testing the annotation pipeline.
"""

import os
import requests
import time
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_image(url, filename, output_dir="data/real_traffic"):
    """Download an image from URL."""
    try:
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Download image
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Save image
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        logger.info(f"✅ Downloaded: {filename}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to download {filename}: {str(e)}")
        return False

def download_traffic_images():
    """Download a collection of real traffic images."""
    logger.info("🚗 Downloading real traffic images for testing...")
    
    # High-quality traffic images from Unsplash (free to use)
    traffic_images = [
        {
            "url": "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&q=80",
            "filename": "highway_traffic_01.jpg",
            "description": "Highway with multiple vehicles"
        },
        {
            "url": "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=800&q=80",
            "filename": "city_traffic_01.jpg", 
            "description": "City street with cars and pedestrians"
        },
        {
            "url": "https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=800&q=80",
            "filename": "highway_cars_01.jpg",
            "description": "Highway with cars and trucks"
        },
        {
            "url": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&q=80",
            "filename": "urban_street_01.jpg",
            "description": "Urban street scene"
        },
        {
            "url": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80",
            "filename": "busy_road_01.jpg",
            "description": "Busy road with multiple vehicles"
        },
        {
            "url": "https://images.unsplash.com/photo-1573348722427-f1d6819fdf98?w=800&q=80",
            "filename": "intersection_01.jpg",
            "description": "Traffic intersection"
        },
        {
            "url": "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&q=80",
            "filename": "parking_lot_01.jpg",
            "description": "Parking lot with cars"
        },
        {
            "url": "https://images.unsplash.com/photo-1486162928267-e6274cb3106f?w=800&q=80",
            "filename": "street_view_01.jpg",
            "description": "Street view with vehicles"
        }
    ]
    
    successful_downloads = 0
    
    for i, img_info in enumerate(traffic_images):
        logger.info(f"Downloading {i+1}/{len(traffic_images)}: {img_info['description']}")
        
        success = download_image(
            img_info["url"], 
            img_info["filename"], 
            "data/real_traffic"
        )
        
        if success:
            successful_downloads += 1
        
        # Be respectful to the server
        time.sleep(1)
    
    logger.info(f"🎉 Downloaded {successful_downloads}/{len(traffic_images)} images successfully!")
    
    if successful_downloads > 0:
        logger.info("📁 Images saved to: data/real_traffic/")
        logger.info("🚀 Next steps:")
        logger.info("1. Copy images to data/raw/ for annotation")
        logger.info("2. Run: streamlit run src/annotation/simple_app.py")
        logger.info("3. Test auto-detection on real traffic images!")
    
    return successful_downloads > 0

def setup_real_image_testing():
    """Set up the directory structure for real image testing."""
    logger.info("🔧 Setting up real image testing environment...")
    
    # Create directories
    directories = [
        "data/real_traffic",
        "data/real_annotations", 
        "data/real_processed"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"📁 Created directory: {directory}")
    
    # Download images
    success = download_traffic_images()
    
    if success:
        # Copy images to raw directory for processing
        import shutil
        
        real_traffic_dir = "data/real_traffic"
        raw_dir = "data/raw"
        
        if os.path.exists(real_traffic_dir):
            for filename in os.listdir(real_traffic_dir):
                if filename.endswith(('.jpg', '.jpeg', '.png')):
                    src = os.path.join(real_traffic_dir, filename)
                    dst = os.path.join(raw_dir, filename)
                    shutil.copy2(src, dst)
                    logger.info(f"📋 Copied {filename} to raw directory")
        
        logger.info("✅ Real image testing setup complete!")
        return True
    else:
        logger.error("❌ Failed to download images")
        return False

def main():
    """Main function."""
    print("🚗 Real Traffic Image Downloader")
    print("=" * 50)
    
    try:
        success = setup_real_image_testing()
        
        if success:
            print("\n🎉 Setup completed successfully!")
            print("\n📋 What's next:")
            print("1. 🌐 Launch annotation interface:")
            print("   streamlit run src/annotation/simple_app.py")
            print("\n2. 🖼️ Navigate through the real traffic images")
            print("3. 🤖 Test auto-detection (if PyTorch/ultralytics installed)")
            print("4. ✏️ Add manual annotations")
            print("5. 📊 Compare quality scores between synthetic and real images")
            
            print("\n🔧 For full YOLO detection:")
            print("   pip install torch ultralytics")
            
            print("\n📁 Image locations:")
            print("   - Original: data/real_traffic/")
            print("   - For annotation: data/raw/")
            print("   - Annotations will be saved to: data/annotations/")
            
        else:
            print("\n❌ Setup failed. You can manually add traffic images to data/raw/")
            
    except KeyboardInterrupt:
        print("\n👋 Download cancelled by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
