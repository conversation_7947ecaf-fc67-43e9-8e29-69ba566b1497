"""
Unit tests for annotation utilities.
"""

import unittest
import numpy as np
import tempfile
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from src.utils.annotation_utils import (
    AnnotationHandler, calculate_iou, validate_bbox, ROAD_ELEMENT_CLASSES
)

class TestAnnotationUtils(unittest.TestCase):
    """Test cases for annotation utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.handler = AnnotationHandler()
        self.temp_dir = tempfile.mkdtemp()
        
        # Sample annotation data
        self.sample_annotations = [
            {
                'bbox': [100, 100, 200, 200],
                'class_id': 0,
                'class_name': 'vehicle',
                'confidence': 0.9
            },
            {
                'bbox': [300, 150, 400, 250],
                'class_id': 1,
                'class_name': 'pedestrian',
                'confidence': 0.8
            }
        ]
        
        self.image_width = 640
        self.image_height = 480
    
    def test_calculate_iou(self):
        """Test IoU calculation."""
        # Test identical boxes
        bbox1 = [100, 100, 200, 200]
        bbox2 = [100, 100, 200, 200]
        iou = calculate_iou(bbox1, bbox2)
        self.assertAlmostEqual(iou, 1.0, places=5)
        
        # Test non-overlapping boxes
        bbox1 = [100, 100, 200, 200]
        bbox2 = [300, 300, 400, 400]
        iou = calculate_iou(bbox1, bbox2)
        self.assertAlmostEqual(iou, 0.0, places=5)
        
        # Test partially overlapping boxes
        bbox1 = [100, 100, 200, 200]
        bbox2 = [150, 150, 250, 250]
        iou = calculate_iou(bbox1, bbox2)
        self.assertGreater(iou, 0.0)
        self.assertLess(iou, 1.0)
    
    def test_validate_bbox(self):
        """Test bounding box validation."""
        # Valid bbox
        bbox = [100, 100, 200, 200]
        self.assertTrue(validate_bbox(bbox, self.image_width, self.image_height))
        
        # Invalid bbox - outside image bounds
        bbox = [700, 100, 800, 200]
        self.assertFalse(validate_bbox(bbox, self.image_width, self.image_height))
        
        # Invalid bbox - x2 <= x1
        bbox = [200, 100, 100, 200]
        self.assertFalse(validate_bbox(bbox, self.image_width, self.image_height))
        
        # Invalid bbox - y2 <= y1
        bbox = [100, 200, 200, 100]
        self.assertFalse(validate_bbox(bbox, self.image_width, self.image_height))
    
    def test_yolo_annotation_save_load(self):
        """Test YOLO annotation save and load."""
        # Save annotations
        output_path = os.path.join(self.temp_dir, 'test_annotations.txt')
        success = self.handler.save_yolo_annotation(
            self.sample_annotations, output_path, 
            self.image_width, self.image_height
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(output_path))
        
        # Load annotations
        loaded_annotations = self.handler.load_yolo_annotation(
            output_path, self.image_width, self.image_height
        )
        
        self.assertEqual(len(loaded_annotations), len(self.sample_annotations))
        
        # Check first annotation
        original = self.sample_annotations[0]
        loaded = loaded_annotations[0]
        
        self.assertEqual(loaded['class_id'], original['class_id'])
        self.assertEqual(loaded['class_name'], original['class_name'])
        
        # Check bbox coordinates (with some tolerance for float conversion)
        for i in range(4):
            self.assertAlmostEqual(loaded['bbox'][i], original['bbox'][i], delta=1.0)
    
    def test_coco_format_conversion(self):
        """Test COCO format conversion."""
        image_info = {
            "id": 1,
            "width": self.image_width,
            "height": self.image_height,
            "file_name": "test_image.jpg"
        }
        
        coco_data = self.handler.convert_to_coco_format(
            self.sample_annotations, image_info
        )
        
        # Check structure
        self.assertIn('images', coco_data)
        self.assertIn('annotations', coco_data)
        self.assertIn('categories', coco_data)
        
        # Check content
        self.assertEqual(len(coco_data['images']), 1)
        self.assertEqual(len(coco_data['annotations']), len(self.sample_annotations))
        self.assertEqual(len(coco_data['categories']), len(ROAD_ELEMENT_CLASSES))
        
        # Check annotation format
        coco_ann = coco_data['annotations'][0]
        original_ann = self.sample_annotations[0]
        
        self.assertEqual(coco_ann['category_id'], original_ann['class_id'])
        self.assertEqual(coco_ann['image_id'], image_info['id'])
        
        # Check bbox format (COCO uses [x, y, width, height])
        x1, y1, x2, y2 = original_ann['bbox']
        expected_bbox = [x1, y1, x2 - x1, y2 - y1]
        self.assertEqual(coco_ann['bbox'], expected_bbox)
    
    def test_class_mapping(self):
        """Test class mapping functionality."""
        # Test default mapping
        self.assertEqual(self.handler.class_mapping[0], 'vehicle')
        self.assertEqual(self.handler.class_mapping[1], 'pedestrian')
        
        # Test reverse mapping
        self.assertEqual(self.handler.reverse_mapping['vehicle'], 0)
        self.assertEqual(self.handler.reverse_mapping['pedestrian'], 1)
        
        # Test custom mapping
        custom_mapping = {0: 'car', 1: 'person'}
        custom_handler = AnnotationHandler(custom_mapping)
        self.assertEqual(custom_handler.class_mapping[0], 'car')
        self.assertEqual(custom_handler.reverse_mapping['car'], 0)
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

if __name__ == '__main__':
    unittest.main()
