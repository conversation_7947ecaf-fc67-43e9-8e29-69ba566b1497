"""
Setup script for the Road Element Annotation Assistant.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    logger.info(f"Python version: {sys.version}")
    return True

def install_requirements():
    """Install required packages."""
    logger.info("Installing required packages...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        logger.info("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install requirements: {e}")
        return False

def setup_directories():
    """Create necessary directories."""
    logger.info("Setting up directories...")
    
    directories = [
        "data/raw",
        "data/processed", 
        "data/annotations",
        "models",
        "outputs",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")
    
    return True

def download_sample_data():
    """Download and setup sample data."""
    logger.info("Setting up sample data...")
    
    try:
        from src.utils.data_downloader import DataDownloader
        
        downloader = DataDownloader("data")
        success = downloader.setup_sample_dataset(
            use_synthetic=True, 
            download_samples=False
        )
        
        if success:
            logger.info("✅ Sample data setup complete")
        else:
            logger.warning("⚠️ Sample data setup had issues")
        
        return success
    
    except Exception as e:
        logger.error(f"❌ Error setting up sample data: {e}")
        return False

def run_tests():
    """Run basic tests to verify installation."""
    logger.info("Running basic tests...")
    
    try:
        # Test imports
        from src.utils.image_utils import ImageProcessor
        from src.utils.annotation_utils import AnnotationHandler
        from src.models.yolo_detector import RoadElementDetector
        from src.quality_assurance.validator import AnnotationValidator
        
        logger.info("✅ All imports successful")
        
        # Test basic functionality
        processor = ImageProcessor()
        handler = AnnotationHandler()
        validator = AnnotationValidator()
        
        logger.info("✅ Basic functionality test passed")
        return True
    
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def create_config_file():
    """Create a default configuration file."""
    logger.info("Creating configuration file...")
    
    config = {
        "data": {
            "raw_dir": "data/raw",
            "processed_dir": "data/processed",
            "annotations_dir": "data/annotations"
        },
        "model": {
            "yolo_model": "yolov8n.pt",
            "confidence_threshold": 0.5,
            "iou_threshold": 0.5
        },
        "annotation": {
            "classes": ["vehicle", "pedestrian", "signboard", "traffic_light", "lane_marking"],
            "target_image_size": [640, 640]
        },
        "quality_assurance": {
            "min_bbox_area": 100,
            "max_bbox_area": 500000,
            "min_aspect_ratio": 0.1,
            "max_aspect_ratio": 10.0
        }
    }
    
    import json
    with open("config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    logger.info("✅ Configuration file created: config.json")
    return True

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\n📋 Next Steps:")
    print("1. Activate your virtual environment (if using one)")
    print("2. Run the annotation tool:")
    print("   streamlit run src/annotation/app.py")
    print("\n3. Or explore the analysis notebook:")
    print("   jupyter notebook notebooks/annotation_analysis.ipynb")
    print("\n4. To run tests:")
    print("   python -m pytest tests/")
    print("\n📁 Directory Structure:")
    print("   data/raw/        - Place your images here")
    print("   data/processed/  - Preprocessed images")
    print("   data/annotations/ - Generated annotations")
    print("   src/             - Source code")
    print("   notebooks/       - Analysis notebooks")
    print("\n🔧 Configuration:")
    print("   Edit config.json to customize settings")
    print("\n📚 Documentation:")
    print("   See README.md for detailed instructions")
    print("="*60)

def main():
    """Main setup function."""
    print("🚗 Road Element Annotation Assistant Setup")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Setup steps
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing requirements", install_requirements),
        ("Setting up directories", setup_directories),
        ("Creating configuration", create_config_file),
        ("Setting up sample data", download_sample_data),
        ("Running tests", run_tests)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        logger.info(f"Step: {step_name}")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            logger.error(f"Error in {step_name}: {e}")
            failed_steps.append(step_name)
    
    # Summary
    if failed_steps:
        print(f"\n⚠️ Setup completed with {len(failed_steps)} issues:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nYou may need to resolve these issues manually.")
    else:
        print("\n✅ Setup completed successfully!")
    
    print_next_steps()

if __name__ == "__main__":
    main()
