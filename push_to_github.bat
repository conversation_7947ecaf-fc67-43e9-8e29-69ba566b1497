@echo off
echo ========================================
echo  Road Element Annotation Assistant
echo  GitHub Push Script
echo ========================================
echo.

echo Step 1: Checking Git configuration...
git config --global user.name "Harshith1201"
git config --global user.email "<EMAIL>"

echo.
echo Step 2: Checking repository status...
git status

echo.
echo Step 3: Adding remote repository...
git remote remove origin 2>nul
git remote add origin https://github.com/Harshith1201/road-element-annotation-assistant.git

echo.
echo Step 4: Setting main branch...
git branch -M main

echo.
echo Step 5: Pushing to GitHub...
echo NOTE: You will be prompted for GitHub credentials
echo Username: Harshith1201
echo Password: Use your GitHub Personal Access Token
echo.
git push -u origin main

echo.
echo ========================================
echo Push completed! Check your repository at:
echo https://github.com/Harshith1201/road-element-annotation-assistant
echo ========================================
pause
