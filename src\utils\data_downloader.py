"""
Data downloader and preprocessor for sample road images.
"""

import os
import requests
import zipfile
import shutil
from typing import List, Optional
import logging
from PIL import Image
import numpy as np

logger = logging.getLogger(__name__)

class DataDownloader:
    """Downloads and preprocesses sample road images for annotation."""
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize data downloader.
        
        Args:
            data_dir: Base data directory
        """
        self.data_dir = data_dir
        self.raw_dir = os.path.join(data_dir, "raw")
        self.processed_dir = os.path.join(data_dir, "processed")
        
        # Create directories
        os.makedirs(self.raw_dir, exist_ok=True)
        os.makedirs(self.processed_dir, exist_ok=True)
    
    def download_sample_images(self) -> bool:
        """
        Download sample road images from public sources.
        
        Returns:
            True if successful, False otherwise
        """
        # Sample URLs for road images (these are example URLs - replace with actual sources)
        sample_urls = [
            "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=640",  # Road scene
            "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=640",  # Traffic
            "https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=640",   # Highway
            "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=640",   # City street
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=640", # Urban road
        ]
        
        logger.info("Downloading sample road images...")
        
        for i, url in enumerate(sample_urls):
            try:
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                
                # Save image
                filename = f"sample_road_{i+1:02d}.jpg"
                filepath = os.path.join(self.raw_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"Downloaded: {filename}")
                
            except Exception as e:
                logger.error(f"Error downloading image {i+1}: {str(e)}")
                continue
        
        return True
    
    def create_synthetic_images(self, count: int = 5) -> bool:
        """
        Create synthetic road scene images for testing.
        
        Args:
            count: Number of synthetic images to create
            
        Returns:
            True if successful
        """
        logger.info(f"Creating {count} synthetic road images...")
        
        try:
            for i in range(count):
                # Create a simple synthetic road scene
                image = self._create_synthetic_road_scene()
                
                # Save image
                filename = f"synthetic_road_{i+1:02d}.jpg"
                filepath = os.path.join(self.raw_dir, filename)
                
                image.save(filepath)
                logger.info(f"Created: {filename}")
            
            return True
        
        except Exception as e:
            logger.error(f"Error creating synthetic images: {str(e)}")
            return False
    
    def _create_synthetic_road_scene(self) -> Image.Image:
        """
        Create a simple synthetic road scene.
        
        Returns:
            PIL Image of synthetic road scene
        """
        # Create base image
        width, height = 640, 480
        image = Image.new('RGB', (width, height), color=(135, 206, 235))  # Sky blue
        
        # Convert to numpy for easier manipulation
        img_array = np.array(image)
        
        # Add road (gray rectangle)
        road_y_start = int(height * 0.6)
        img_array[road_y_start:, :] = [64, 64, 64]  # Dark gray road
        
        # Add lane markings (white lines)
        lane_center = width // 2
        for y in range(road_y_start, height, 20):
            if y + 10 < height:
                img_array[y:y+10, lane_center-2:lane_center+2] = [255, 255, 255]
        
        # Add some buildings (rectangles)
        building_colors = [(139, 69, 19), (160, 82, 45), (105, 105, 105)]
        for i in range(3):
            x_start = i * (width // 3) + 20
            x_end = x_start + (width // 3) - 40
            y_start = int(height * 0.3)
            y_end = road_y_start
            
            color = building_colors[i % len(building_colors)]
            img_array[y_start:y_end, x_start:x_end] = color
        
        # Add some "vehicles" (colored rectangles)
        vehicle_positions = [
            (100, road_y_start + 20, 150, road_y_start + 60),  # Car 1
            (300, road_y_start + 30, 380, road_y_start + 80),  # Car 2
            (500, road_y_start + 15, 580, road_y_start + 70),  # Car 3
        ]
        
        vehicle_colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]  # Red, Green, Blue
        
        for i, (x1, y1, x2, y2) in enumerate(vehicle_positions):
            color = vehicle_colors[i % len(vehicle_colors)]
            img_array[y1:y2, x1:x2] = color
        
        # Convert back to PIL Image
        return Image.fromarray(img_array)
    
    def preprocess_images(self, target_size: tuple = (640, 640)) -> bool:
        """
        Preprocess images to standard format.
        
        Args:
            target_size: Target image size (width, height)
            
        Returns:
            True if successful
        """
        logger.info("Preprocessing images...")
        
        # Get all images in raw directory
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend([f for f in os.listdir(self.raw_dir) if f.lower().endswith(ext)])
        
        if not image_files:
            logger.warning("No images found in raw directory")
            return False
        
        for filename in image_files:
            try:
                # Load image
                input_path = os.path.join(self.raw_dir, filename)
                image = Image.open(input_path)
                
                # Convert to RGB if necessary
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # Resize image
                image = image.resize(target_size, Image.Resampling.LANCZOS)
                
                # Save processed image
                output_filename = os.path.splitext(filename)[0] + '.jpg'
                output_path = os.path.join(self.processed_dir, output_filename)
                image.save(output_path, 'JPEG', quality=95)
                
                logger.info(f"Processed: {filename} -> {output_filename}")
                
            except Exception as e:
                logger.error(f"Error processing {filename}: {str(e)}")
                continue
        
        return True
    
    def setup_sample_dataset(self, use_synthetic: bool = True, 
                           download_samples: bool = False) -> bool:
        """
        Set up a complete sample dataset for annotation.
        
        Args:
            use_synthetic: Whether to create synthetic images
            download_samples: Whether to download sample images
            
        Returns:
            True if successful
        """
        logger.info("Setting up sample dataset...")
        
        success = True
        
        # Create synthetic images
        if use_synthetic:
            success &= self.create_synthetic_images(count=5)
        
        # Download sample images (optional)
        if download_samples:
            try:
                success &= self.download_sample_images()
            except Exception as e:
                logger.warning(f"Could not download sample images: {str(e)}")
                logger.info("Continuing with synthetic images only...")
        
        # Preprocess all images
        success &= self.preprocess_images()
        
        if success:
            logger.info("Sample dataset setup complete!")
            logger.info(f"Raw images: {self.raw_dir}")
            logger.info(f"Processed images: {self.processed_dir}")
        else:
            logger.error("Sample dataset setup failed")
        
        return success

def main():
    """Main function to set up sample dataset."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Download and preprocess sample road images")
    parser.add_argument("--data-dir", default="data", help="Data directory")
    parser.add_argument("--synthetic", action="store_true", default=True, 
                       help="Create synthetic images")
    parser.add_argument("--download", action="store_true", 
                       help="Download sample images from web")
    
    args = parser.parse_args()
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Create downloader and setup dataset
    downloader = DataDownloader(args.data_dir)
    success = downloader.setup_sample_dataset(
        use_synthetic=args.synthetic,
        download_samples=args.download
    )
    
    if success:
        print("\n✅ Sample dataset ready!")
        print(f"📁 Raw images: {downloader.raw_dir}")
        print(f"📁 Processed images: {downloader.processed_dir}")
        print("\n🚀 Next steps:")
        print("1. Run: streamlit run src/annotation/app.py")
        print("2. Start annotating your road images!")
    else:
        print("\n❌ Failed to set up sample dataset")

if __name__ == "__main__":
    main()
