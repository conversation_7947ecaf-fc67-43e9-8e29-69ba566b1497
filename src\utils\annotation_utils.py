"""
Annotation utilities for handling different annotation formats.
"""

import json
import os
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

# Road element class mapping
ROAD_ELEMENT_CLASSES = {
    0: 'vehicle',
    1: 'pedestrian', 
    2: 'signboard',
    3: 'traffic_light',
    4: 'lane_marking'
}

class AnnotationHandler:
    """Handles different annotation formats (COCO, YOLO, Pascal VOC)."""
    
    def __init__(self, class_mapping: Dict[int, str] = None):
        """
        Initialize annotation handler.
        
        Args:
            class_mapping: Dictionary mapping class IDs to class names
        """
        self.class_mapping = class_mapping or ROAD_ELEMENT_CLASSES
        self.reverse_mapping = {v: k for k, v in self.class_mapping.items()}
    
    def load_yolo_annotation(self, annotation_path: str, image_width: int, 
                           image_height: int) -> List[Dict]:
        """
        Load YOLO format annotation.
        
        Args:
            annotation_path: Path to YOLO annotation file
            image_width: Image width
            image_height: Image height
            
        Returns:
            List of annotation dictionaries
        """
        annotations = []
        
        if not os.path.exists(annotation_path):
            return annotations
        
        try:
            with open(annotation_path, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1]) * image_width
                    y_center = float(parts[2]) * image_height
                    width = float(parts[3]) * image_width
                    height = float(parts[4]) * image_height
                    
                    # Convert to x1, y1, x2, y2
                    x1 = x_center - width / 2
                    y1 = y_center - height / 2
                    x2 = x_center + width / 2
                    y2 = y_center + height / 2
                    
                    annotations.append({
                        'class_id': class_id,
                        'class_name': self.class_mapping.get(class_id, 'unknown'),
                        'bbox': [x1, y1, x2, y2],
                        'confidence': 1.0
                    })
        
        except Exception as e:
            logger.error(f"Error loading YOLO annotation {annotation_path}: {str(e)}")
        
        return annotations
    
    def save_yolo_annotation(self, annotations: List[Dict], output_path: str,
                           image_width: int, image_height: int) -> bool:
        """
        Save annotations in YOLO format.
        
        Args:
            annotations: List of annotation dictionaries
            output_path: Output file path
            image_width: Image width
            image_height: Image height
            
        Returns:
            True if successful, False otherwise
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w') as f:
                for ann in annotations:
                    x1, y1, x2, y2 = ann['bbox']
                    
                    # Convert to YOLO format
                    x_center = (x1 + x2) / 2 / image_width
                    y_center = (y1 + y2) / 2 / image_height
                    width = (x2 - x1) / image_width
                    height = (y2 - y1) / image_height
                    
                    class_id = ann.get('class_id', 0)
                    
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} "
                           f"{width:.6f} {height:.6f}\n")
            
            return True
        
        except Exception as e:
            logger.error(f"Error saving YOLO annotation {output_path}: {str(e)}")
            return False
    
    def load_coco_annotation(self, annotation_path: str) -> Dict:
        """
        Load COCO format annotation.
        
        Args:
            annotation_path: Path to COCO annotation file
            
        Returns:
            COCO annotation dictionary
        """
        try:
            with open(annotation_path, 'r') as f:
                coco_data = json.load(f)
            return coco_data
        
        except Exception as e:
            logger.error(f"Error loading COCO annotation {annotation_path}: {str(e)}")
            return {}
    
    def convert_to_coco_format(self, annotations: List[Dict], image_info: Dict) -> Dict:
        """
        Convert annotations to COCO format.
        
        Args:
            annotations: List of annotation dictionaries
            image_info: Image information dictionary
            
        Returns:
            COCO format dictionary
        """
        coco_data = {
            "images": [image_info],
            "annotations": [],
            "categories": [
                {"id": class_id, "name": class_name} 
                for class_id, class_name in self.class_mapping.items()
            ]
        }
        
        for idx, ann in enumerate(annotations):
            x1, y1, x2, y2 = ann['bbox']
            width = x2 - x1
            height = y2 - y1
            
            coco_ann = {
                "id": idx + 1,
                "image_id": image_info["id"],
                "category_id": ann.get('class_id', 0),
                "bbox": [x1, y1, width, height],
                "area": width * height,
                "iscrowd": 0
            }
            coco_data["annotations"].append(coco_ann)
        
        return coco_data

def calculate_iou(bbox1: List[float], bbox2: List[float]) -> float:
    """
    Calculate Intersection over Union (IoU) between two bounding boxes.
    
    Args:
        bbox1: First bounding box [x1, y1, x2, y2]
        bbox2: Second bounding box [x1, y1, x2, y2]
        
    Returns:
        IoU value between 0 and 1
    """
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)
    
    # Calculate union area
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - intersection_area
    
    if union_area == 0:
        return 0.0
    
    return intersection_area / union_area

def validate_bbox(bbox: List[float], image_width: int, image_height: int) -> bool:
    """
    Validate bounding box coordinates.
    
    Args:
        bbox: Bounding box [x1, y1, x2, y2]
        image_width: Image width
        image_height: Image height
        
    Returns:
        True if valid, False otherwise
    """
    x1, y1, x2, y2 = bbox
    
    # Check if coordinates are within image bounds
    if x1 < 0 or y1 < 0 or x2 > image_width or y2 > image_height:
        return False
    
    # Check if x2 > x1 and y2 > y1
    if x2 <= x1 or y2 <= y1:
        return False
    
    # Check minimum size (at least 1x1 pixel)
    if (x2 - x1) < 1 or (y2 - y1) < 1:
        return False
    
    return True
