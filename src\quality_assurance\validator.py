"""
Quality assurance validator for annotation consistency and accuracy.
"""

import os
import json
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from datetime import datetime

from ..utils.annotation_utils import calculate_iou, validate_bbox

logger = logging.getLogger(__name__)

class AnnotationValidator:
    """Validates annotation quality and consistency."""
    
    def __init__(self, config: Dict = None):
        """
        Initialize the annotation validator.
        
        Args:
            config: Configuration dictionary with validation parameters
        """
        self.config = config or self._get_default_config()
        self.validation_results = []
        
    def _get_default_config(self) -> Dict:
        """Get default validation configuration."""
        return {
            'min_bbox_area': 100,           # Minimum bounding box area
            'max_bbox_area': 500000,        # Maximum bounding box area
            'min_aspect_ratio': 0.1,        # Minimum width/height ratio
            'max_aspect_ratio': 10.0,       # Maximum width/height ratio
            'iou_threshold': 0.5,           # IoU threshold for overlap detection
            'confidence_threshold': 0.3,    # Minimum confidence for auto-annotations
            'class_consistency_threshold': 0.8,  # Threshold for class consistency
        }
    
    def validate_single_annotation(self, annotations: List[Dict], 
                                 image_width: int, image_height: int,
                                 image_path: str = "") -> Dict:
        """
        Validate annotations for a single image.
        
        Args:
            annotations: List of annotation dictionaries
            image_width: Image width
            image_height: Image height
            image_path: Path to the image file
            
        Returns:
            Validation results dictionary
        """
        results = {
            'image_path': image_path,
            'total_annotations': len(annotations),
            'valid_annotations': 0,
            'errors': [],
            'warnings': [],
            'bbox_issues': [],
            'overlap_issues': [],
            'class_distribution': {}
        }
        
        # Check each annotation
        for i, ann in enumerate(annotations):
            bbox = ann.get('bbox', [])
            class_name = ann.get('class_name', 'unknown')
            confidence = ann.get('confidence', 1.0)
            
            # Update class distribution
            results['class_distribution'][class_name] = \
                results['class_distribution'].get(class_name, 0) + 1
            
            # Validate bounding box
            bbox_valid, bbox_errors = self._validate_bbox(
                bbox, image_width, image_height, i
            )
            
            if bbox_valid:
                results['valid_annotations'] += 1
            else:
                results['bbox_issues'].extend(bbox_errors)
            
            # Check confidence threshold
            if confidence < self.config['confidence_threshold']:
                results['warnings'].append(
                    f"Annotation {i}: Low confidence ({confidence:.2f})"
                )
            
            # Check class validity
            if class_name == 'unknown':
                results['errors'].append(
                    f"Annotation {i}: Unknown class"
                )
        
        # Check for overlapping annotations
        overlap_issues = self._check_overlaps(annotations)
        results['overlap_issues'] = overlap_issues
        
        # Calculate quality score
        results['quality_score'] = self._calculate_quality_score(results)
        
        return results
    
    def _validate_bbox(self, bbox: List[float], image_width: int, 
                      image_height: int, ann_index: int) -> Tuple[bool, List[str]]:
        """
        Validate a single bounding box.
        
        Args:
            bbox: Bounding box coordinates [x1, y1, x2, y2]
            image_width: Image width
            image_height: Image height
            ann_index: Annotation index
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        if len(bbox) != 4:
            errors.append(f"Annotation {ann_index}: Invalid bbox format")
            return False, errors
        
        x1, y1, x2, y2 = bbox
        
        # Check basic validity
        if not validate_bbox(bbox, image_width, image_height):
            errors.append(f"Annotation {ann_index}: Bbox outside image bounds")
        
        # Check area constraints
        area = (x2 - x1) * (y2 - y1)
        if area < self.config['min_bbox_area']:
            errors.append(f"Annotation {ann_index}: Bbox too small (area: {area})")
        elif area > self.config['max_bbox_area']:
            errors.append(f"Annotation {ann_index}: Bbox too large (area: {area})")
        
        # Check aspect ratio
        width = x2 - x1
        height = y2 - y1
        if height > 0:
            aspect_ratio = width / height
            if aspect_ratio < self.config['min_aspect_ratio']:
                errors.append(f"Annotation {ann_index}: Aspect ratio too small ({aspect_ratio:.2f})")
            elif aspect_ratio > self.config['max_aspect_ratio']:
                errors.append(f"Annotation {ann_index}: Aspect ratio too large ({aspect_ratio:.2f})")
        
        return len(errors) == 0, errors
    
    def _check_overlaps(self, annotations: List[Dict]) -> List[str]:
        """
        Check for overlapping annotations of the same class.
        
        Args:
            annotations: List of annotations
            
        Returns:
            List of overlap issues
        """
        overlap_issues = []
        
        for i in range(len(annotations)):
            for j in range(i + 1, len(annotations)):
                ann1, ann2 = annotations[i], annotations[j]
                
                # Only check overlaps for same class
                if ann1.get('class_name') == ann2.get('class_name'):
                    iou = calculate_iou(ann1['bbox'], ann2['bbox'])
                    
                    if iou > self.config['iou_threshold']:
                        overlap_issues.append(
                            f"High overlap (IoU: {iou:.2f}) between annotations {i} and {j}"
                        )
        
        return overlap_issues
    
    def _calculate_quality_score(self, results: Dict) -> float:
        """
        Calculate overall quality score for annotations.
        
        Args:
            results: Validation results
            
        Returns:
            Quality score between 0 and 1
        """
        total_annotations = results['total_annotations']
        if total_annotations == 0:
            return 1.0
        
        # Base score from valid annotations
        validity_score = results['valid_annotations'] / total_annotations
        
        # Penalty for errors and warnings
        error_penalty = min(len(results['errors']) * 0.1, 0.5)
        warning_penalty = min(len(results['warnings']) * 0.05, 0.3)
        overlap_penalty = min(len(results['overlap_issues']) * 0.1, 0.4)
        
        quality_score = validity_score - error_penalty - warning_penalty - overlap_penalty
        return max(0.0, min(1.0, quality_score))
    
    def validate_dataset(self, annotation_dir: str, image_dir: str) -> Dict:
        """
        Validate entire dataset.
        
        Args:
            annotation_dir: Directory containing annotation files
            image_dir: Directory containing images
            
        Returns:
            Dataset validation results
        """
        dataset_results = {
            'total_images': 0,
            'validated_images': 0,
            'average_quality_score': 0.0,
            'class_distribution': {},
            'common_errors': {},
            'image_results': []
        }
        
        # Get annotation files
        annotation_files = [f for f in os.listdir(annotation_dir) 
                          if f.endswith('.txt') or f.endswith('.json')]
        
        total_quality_score = 0.0
        
        for ann_file in annotation_files:
            # Load annotations (assuming YOLO format for now)
            ann_path = os.path.join(annotation_dir, ann_file)
            image_name = os.path.splitext(ann_file)[0] + '.jpg'  # Assume jpg
            image_path = os.path.join(image_dir, image_name)
            
            if not os.path.exists(image_path):
                # Try other extensions
                for ext in ['.png', '.jpeg', '.bmp']:
                    alt_path = os.path.join(image_dir, os.path.splitext(ann_file)[0] + ext)
                    if os.path.exists(alt_path):
                        image_path = alt_path
                        break
                else:
                    logger.warning(f"Image not found for annotation: {ann_file}")
                    continue
            
            # Get image dimensions (simplified - would need actual image loading)
            image_width, image_height = 640, 640  # Default size
            
            # Load annotations (simplified for YOLO format)
            annotations = self._load_yolo_annotations(ann_path, image_width, image_height)
            
            # Validate
            results = self.validate_single_annotation(
                annotations, image_width, image_height, image_path
            )
            
            dataset_results['image_results'].append(results)
            dataset_results['total_images'] += 1
            
            if results['quality_score'] > 0.5:  # Consider as validated if score > 0.5
                dataset_results['validated_images'] += 1
            
            total_quality_score += results['quality_score']
            
            # Aggregate class distribution
            for class_name, count in results['class_distribution'].items():
                dataset_results['class_distribution'][class_name] = \
                    dataset_results['class_distribution'].get(class_name, 0) + count
            
            # Aggregate common errors
            for error in results['errors']:
                error_type = error.split(':')[0] if ':' in error else error
                dataset_results['common_errors'][error_type] = \
                    dataset_results['common_errors'].get(error_type, 0) + 1
        
        # Calculate average quality score
        if dataset_results['total_images'] > 0:
            dataset_results['average_quality_score'] = \
                total_quality_score / dataset_results['total_images']
        
        return dataset_results
    
    def _load_yolo_annotations(self, ann_path: str, image_width: int, 
                              image_height: int) -> List[Dict]:
        """Load YOLO format annotations (simplified)."""
        annotations = []
        
        try:
            with open(ann_path, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1]) * image_width
                    y_center = float(parts[2]) * image_height
                    width = float(parts[3]) * image_width
                    height = float(parts[4]) * image_height
                    
                    x1 = x_center - width / 2
                    y1 = y_center - height / 2
                    x2 = x_center + width / 2
                    y2 = y_center + height / 2
                    
                    class_names = {0: 'vehicle', 1: 'pedestrian', 2: 'signboard', 
                                 3: 'traffic_light', 4: 'lane_marking'}
                    
                    annotations.append({
                        'bbox': [x1, y1, x2, y2],
                        'class_id': class_id,
                        'class_name': class_names.get(class_id, 'unknown'),
                        'confidence': 1.0
                    })
        
        except Exception as e:
            logger.error(f"Error loading annotations from {ann_path}: {str(e)}")
        
        return annotations
    
    def generate_report(self, results: Dict, output_path: str) -> bool:
        """
        Generate validation report.
        
        Args:
            results: Validation results
            output_path: Output file path
            
        Returns:
            True if successful
        """
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_images': results['total_images'],
                    'validated_images': results['validated_images'],
                    'validation_rate': results['validated_images'] / max(results['total_images'], 1),
                    'average_quality_score': results['average_quality_score']
                },
                'class_distribution': results['class_distribution'],
                'common_errors': results['common_errors'],
                'detailed_results': results['image_results']
            }
            
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Validation report saved: {output_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            return False
